'use client';

import { useState, useCallback } from 'react';
import useSWR from 'swr';

import { toast } from 'src/components/snackbar';

import { fetchData, createData, updateData, deleteData } from './supabase-utils';
import { ORDER_STATUS_OPTIONS, getOrderStatusInfo } from './order-constants';

/**
 * =====================================================
 * CUSTOM WORKFLOW SERVICE
 * =====================================================
 * 
 * Quản lý workflow tùy chỉnh cho đơn hàng:
 * - Tạo, cập nhật, xóa workflow
 * - Quản lý stages và transitions
 * - Validation business rules
 * - Integration với order management
 */

// Table names
const WORKFLOWS_TABLE = 'order_workflows';
const STAGES_TABLE = 'workflow_stages';
const TRANSITIONS_TABLE = 'order_workflow_transitions';
const INSTANCES_TABLE = 'order_workflow_instances';
const HISTORY_TABLE = 'order_workflow_history';

// Default workflow templates
export const DEFAULT_WORKFLOW_TEMPLATES = {
  simple: {
    name: 'Quy trình sản phẩm đơn giản',
    description: 'Quy trình chuẩn cho sản phẩm vật lý đơn giản',
    product_type: 'simple',
    stages: [
      { name: 'Chờ xác nhận', status_code: 'pending', color: 'warning', sort_order: 1, is_start_stage: true },
      { name: 'Đã xác nhận', status_code: 'confirmed', color: 'info', sort_order: 2 },
      { name: 'Đã thanh toán', status_code: 'paid', color: 'success', sort_order: 3, requires_payment: true },
      { name: 'Đang đóng gói', status_code: 'packaging', color: 'info', sort_order: 4, requires_inventory: true },
      { name: 'Đang vận chuyển', status_code: 'shipping', color: 'primary', sort_order: 5 },
      { name: 'Đã giao hàng', status_code: 'delivered', color: 'success', sort_order: 6 },
      { name: 'Hoàn thành', status_code: 'completed', color: 'success', sort_order: 7, is_end_stage: true }
    ]
  },
  digital: {
    name: 'Quy trình sản phẩm số',
    description: 'Quy trình cho sản phẩm số và download',
    product_type: 'digital',
    stages: [
      { name: 'Chờ xác nhận', status_code: 'pending', color: 'warning', sort_order: 1, is_start_stage: true },
      { name: 'Đã xác nhận', status_code: 'confirmed', color: 'info', sort_order: 2 },
      { name: 'Đã thanh toán', status_code: 'paid', color: 'success', sort_order: 3, requires_payment: true },
      { name: 'Đang chuẩn bị', status_code: 'preparing', color: 'info', sort_order: 4 },
      { name: 'Sẵn sàng tải xuống', status_code: 'ready_download', color: 'success', sort_order: 5 },
      { name: 'Đã gửi', status_code: 'sent', color: 'success', sort_order: 6 },
      { name: 'Hoàn thành', status_code: 'completed', color: 'success', sort_order: 7, is_end_stage: true }
    ]
  },
  service: {
    name: 'Quy trình dịch vụ',
    description: 'Quy trình cho các dịch vụ và booking',
    product_type: 'service',
    stages: [
      { name: 'Chờ xác nhận', status_code: 'pending', color: 'warning', sort_order: 1, is_start_stage: true },
      { name: 'Đã xác nhận', status_code: 'confirmed', color: 'info', sort_order: 2 },
      { name: 'Đã thanh toán', status_code: 'paid', color: 'success', sort_order: 3, requires_payment: true },
      { name: 'Đang lên lịch', status_code: 'scheduling', color: 'warning', sort_order: 4 },
      { name: 'Đang thực hiện', status_code: 'in_progress', color: 'primary', sort_order: 5 },
      { name: 'Đã cung cấp', status_code: 'provided', color: 'success', sort_order: 6 },
      { name: 'Hoàn thành', status_code: 'completed', color: 'success', sort_order: 7, is_end_stage: true }
    ]
  }
};

/**
 * Lấy danh sách workflows
 * @param {Object} options - Tùy chọn filter
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getWorkflows(options = {}) {
  const { productType, isActive = true } = options;
  
  const filters = { is_active: isActive };
  if (productType && productType !== 'all') {
    filters.product_type = productType;
  }

  return fetchData(WORKFLOWS_TABLE, {
    filters,
    orderBy: 'created_at',
    ascending: false,
    select: `
      *,
      stages:workflow_stages(
        *,
        transitions_from:order_workflow_transitions!from_stage_id(*)
      )
    `
  });
}

/**
 * Lấy chi tiết workflow
 * @param {string} workflowId - ID workflow
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function getWorkflowDetail(workflowId) {
  if (!workflowId) {
    return { success: false, error: 'Thiếu ID workflow', data: null };
  }

  return fetchData(WORKFLOWS_TABLE, {
    filters: { id: workflowId },
    select: `
      *,
      stages:workflow_stages(
        *,
        transitions_from:order_workflow_transitions!from_stage_id(*),
        transitions_to:order_workflow_transitions!to_stage_id(*)
      )
    `
  });
}

/**
 * Tạo workflow mới
 * @param {Object} workflowData - Dữ liệu workflow
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createWorkflow(workflowData) {
  try {
    const { stages = [], ...workflowInfo } = workflowData;

    // 1. Tạo workflow
    const workflowResult = await createData(WORKFLOWS_TABLE, workflowInfo);
    if (!workflowResult.success) {
      throw new Error(workflowResult.error?.message || 'Không thể tạo workflow');
    }

    const workflow = workflowResult.data[0];

    // 2. Tạo stages
    if (stages.length > 0) {
      const stagesData = stages.map(stage => ({
        ...stage,
        workflow_id: workflow.id
      }));

      const stagesResult = await createData(STAGES_TABLE, stagesData);
      if (!stagesResult.success) {
        // Rollback workflow nếu tạo stages thất bại
        await deleteData(WORKFLOWS_TABLE, { id: workflow.id });
        throw new Error(stagesResult.error?.message || 'Không thể tạo stages');
      }

      // 3. Tạo transitions tự động giữa các stages liên tiếp
      const createdStages = stagesResult.data.sort((a, b) => a.sort_order - b.sort_order);
      const transitions = [];

      for (let i = 0; i < createdStages.length - 1; i++) {
        transitions.push({
          workflow_id: workflow.id,
          from_stage_id: createdStages[i].id,
          to_stage_id: createdStages[i + 1].id,
          name: `${createdStages[i].name} → ${createdStages[i + 1].name}`,
          is_automatic: false,
          requires_confirmation: true
        });
      }

      if (transitions.length > 0) {
        await createData(TRANSITIONS_TABLE, transitions);
      }
    }

    return {
      success: true,
      data: workflow,
      message: `Đã tạo workflow "${workflow.name}" thành công`
    };

  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi tạo workflow'
    };
  }
}

/**
 * Cập nhật workflow
 * @param {string} workflowId - ID workflow
 * @param {Object} updateData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateWorkflow(workflowId, updateData) {
  if (!workflowId) {
    return { success: false, error: 'Thiếu ID workflow' };
  }

  try {
    const result = await updateData(WORKFLOWS_TABLE, updateData, { id: workflowId });
    
    if (result.success) {
      return {
        success: true,
        data: result.data[0],
        message: 'Đã cập nhật workflow thành công'
      };
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi cập nhật workflow'
    };
  }
}

/**
 * Xóa workflow
 * @param {string} workflowId - ID workflow
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteWorkflow(workflowId) {
  if (!workflowId) {
    return { success: false, error: 'Thiếu ID workflow' };
  }

  try {
    // Kiểm tra xem có đơn hàng nào đang sử dụng workflow này không
    const instancesResult = await fetchData(INSTANCES_TABLE, {
      filters: { workflow_id: workflowId },
      select: 'id'
    });

    if (instancesResult.success && instancesResult.data.length > 0) {
      return {
        success: false,
        error: 'Không thể xóa workflow đang được sử dụng bởi các đơn hàng'
      };
    }

    const result = await deleteData(WORKFLOWS_TABLE, { id: workflowId });
    
    if (result.success) {
      return {
        success: true,
        message: 'Đã xóa workflow thành công'
      };
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi xóa workflow'
    };
  }
}

/**
 * Tạo workflow từ template
 * @param {string} templateType - Loại template (simple, digital, service)
 * @param {Object} customData - Dữ liệu tùy chỉnh
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createWorkflowFromTemplate(templateType, customData = {}) {
  const template = DEFAULT_WORKFLOW_TEMPLATES[templateType];
  if (!template) {
    return {
      success: false,
      error: `Template "${templateType}" không tồn tại`
    };
  }

  const workflowData = {
    ...template,
    ...customData,
    name: customData.name || template.name,
    description: customData.description || template.description
  };

  return createWorkflow(workflowData);
}

// =====================================================
// STAGE MANAGEMENT FUNCTIONS
// =====================================================

/**
 * Tạo stage mới trong workflow
 * @param {string} workflowId - ID workflow
 * @param {Object} stageData - Dữ liệu stage
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function createWorkflowStage(workflowId, stageData) {
  if (!workflowId) {
    return { success: false, error: 'Thiếu ID workflow' };
  }

  try {
    // Validate status_code
    const statusInfo = getOrderStatusInfo(stageData.status_code);
    if (!statusInfo) {
      return {
        success: false,
        error: `Trạng thái "${stageData.status_code}" không hợp lệ`
      };
    }

    const stageWithWorkflow = {
      ...stageData,
      workflow_id: workflowId,
      name: stageData.name || statusInfo.label,
      color: stageData.color || statusInfo.color,
      hex_color: stageData.hex_color || statusInfo.hexColor
    };

    const result = await createData(STAGES_TABLE, stageWithWorkflow);

    if (result.success) {
      return {
        success: true,
        data: result.data[0],
        message: `Đã tạo stage "${stageWithWorkflow.name}" thành công`
      };
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi tạo stage'
    };
  }
}

/**
 * Cập nhật stage
 * @param {string} stageId - ID stage
 * @param {Object} updateData - Dữ liệu cập nhật
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function updateWorkflowStage(stageId, updateData) {
  if (!stageId) {
    return { success: false, error: 'Thiếu ID stage' };
  }

  try {
    const result = await updateData(STAGES_TABLE, updateData, { id: stageId });

    if (result.success) {
      return {
        success: true,
        data: result.data[0],
        message: 'Đã cập nhật stage thành công'
      };
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi cập nhật stage'
    };
  }
}

/**
 * Xóa stage
 * @param {string} stageId - ID stage
 * @returns {Promise<Object>} - Kết quả từ API
 */
export async function deleteWorkflowStage(stageId) {
  if (!stageId) {
    return { success: false, error: 'Thiếu ID stage' };
  }

  try {
    // Kiểm tra xem có transitions nào liên quan không
    const transitionsResult = await fetchData(TRANSITIONS_TABLE, {
      filters: {
        or: `from_stage_id.eq.${stageId},to_stage_id.eq.${stageId}`
      },
      select: 'id'
    });

    if (transitionsResult.success && transitionsResult.data.length > 0) {
      return {
        success: false,
        error: 'Không thể xóa stage đang có transitions liên quan'
      };
    }

    const result = await deleteData(STAGES_TABLE, { id: stageId });

    if (result.success) {
      return {
        success: true,
        message: 'Đã xóa stage thành công'
      };
    }

    return result;
  } catch (error) {
    return {
      success: false,
      error: error.message || 'Lỗi khi xóa stage'
    };
  }
}
