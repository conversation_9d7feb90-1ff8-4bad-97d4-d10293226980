'use client';

import { useState, useCallback } from 'react';

import {
  <PERSON>,
  <PERSON>,
  Stack,
  Button,
  Typography,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControlLabel,
  Switch,
  Tooltip,
  Divider
} from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

import { Iconify } from 'src/components/iconify';
import { Label } from 'src/components/label';

import { useWorkflowDetail } from 'src/actions/mooly-chatbot/use-custom-workflow';
import { ORDER_STATUS_OPTIONS, getOrderStatusInfo } from 'src/actions/mooly-chatbot/order-constants';

// ----------------------------------------------------------------------

export default function WorkflowCanvas({ workflow, onUpdate }) {
  const [selectedStage, setSelectedStage] = useState(null);
  
  // Dialogs
  const addStageDialog = useBoolean();
  const editStageDialog = useBoolean();
  
  // Workflow detail hook
  const {
    workflow: detailWorkflow,
    stages,
    sortedStages,
    loading,
    createStage,
    updateStage,
    deleteStage,
    refresh
  } = useWorkflowDetail(workflow?.id);

  const handleCreateStage = useCallback(async (stageData) => {
    const result = await createStage(stageData);
    if (result.success) {
      addStageDialog.onFalse();
      refresh();
      if (onUpdate) onUpdate(detailWorkflow);
    }
    return result;
  }, [createStage, addStageDialog, refresh, onUpdate, detailWorkflow]);

  const handleUpdateStage = useCallback(async (stageId, updateData) => {
    const result = await updateStage(stageId, updateData);
    if (result.success) {
      editStageDialog.onFalse();
      setSelectedStage(null);
      refresh();
      if (onUpdate) onUpdate(detailWorkflow);
    }
    return result;
  }, [updateStage, editStageDialog, refresh, onUpdate, detailWorkflow]);

  const handleDeleteStage = useCallback(async (stageId) => {
    const result = await deleteStage(stageId);
    if (result.success) {
      setSelectedStage(null);
      refresh();
      if (onUpdate) onUpdate(detailWorkflow);
    }
    return result;
  }, [deleteStage, refresh, onUpdate, detailWorkflow]);

  const renderWorkflowInfo = (
    <Box sx={{ mb: 3 }}>
      <Stack direction="row" alignItems="center" justifyContent="space-between" sx={{ mb: 2 }}>
        <Typography variant="h6">
          {workflow.name}
        </Typography>
        <Stack direction="row" spacing={1}>
          {workflow.is_default && (
            <Label color="success">Mặc định</Label>
          )}
          {workflow.is_active ? (
            <Label color="success">Đang hoạt động</Label>
          ) : (
            <Label color="error">Tạm dừng</Label>
          )}
        </Stack>
      </Stack>
      
      {workflow.description && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
          {workflow.description}
        </Typography>
      )}
      
      <Stack direction="row" spacing={2} alignItems="center">
        <Typography variant="body2" color="text.secondary">
          <strong>{sortedStages.length}</strong> bước trong quy trình
        </Typography>
        <Divider orientation="vertical" flexItem />
        <Button
          size="small"
          startIcon={<Iconify icon="solar:add-circle-bold" />}
          onClick={addStageDialog.onTrue}
        >
          Thêm bước
        </Button>
      </Stack>
    </Box>
  );

  const renderStageFlow = (
    <Box>
      <Typography variant="subtitle2" sx={{ mb: 2 }}>
        Luồng quy trình
      </Typography>
      
      <Stack spacing={2}>
        {sortedStages.map((stage, index) => (
          <Box key={stage.id}>
            <StageCard
              stage={stage}
              isSelected={selectedStage?.id === stage.id}
              onClick={() => setSelectedStage(stage)}
              onEdit={() => {
                setSelectedStage(stage);
                editStageDialog.onTrue();
              }}
              onDelete={() => handleDeleteStage(stage.id)}
            />
            
            {index < sortedStages.length - 1 && (
              <Box display="flex" justifyContent="center" py={1}>
                <Iconify 
                  icon="solar:arrow-down-bold" 
                  sx={{ color: 'text.secondary' }}
                />
              </Box>
            )}
          </Box>
        ))}
        
        {sortedStages.length === 0 && (
          <Box
            display="flex"
            flexDirection="column"
            alignItems="center"
            justifyContent="center"
            py={6}
            color="text.secondary"
          >
            <Iconify icon="solar:document-add-bold" width={48} sx={{ mb: 2 }} />
            <Typography variant="h6" gutterBottom>
              Chưa có bước nào
            </Typography>
            <Typography variant="body2" textAlign="center" sx={{ mb: 3 }}>
              Thêm các bước để tạo quy trình xử lý đơn hàng
            </Typography>
            <Button
              variant="contained"
              startIcon={<Iconify icon="solar:add-circle-bold" />}
              onClick={addStageDialog.onTrue}
            >
              Thêm bước đầu tiên
            </Button>
          </Box>
        )}
      </Stack>
    </Box>
  );

  return (
    <>
      <Box>
        {renderWorkflowInfo}
        {renderStageFlow}
      </Box>

      {/* Add Stage Dialog */}
      <StageFormDialog
        open={addStageDialog.value}
        onClose={addStageDialog.onFalse}
        onSubmit={handleCreateStage}
        title="Thêm bước mới"
        loading={loading}
        existingStages={sortedStages}
      />

      {/* Edit Stage Dialog */}
      <StageFormDialog
        open={editStageDialog.value}
        onClose={() => {
          editStageDialog.onFalse();
          setSelectedStage(null);
        }}
        onSubmit={(data) => handleUpdateStage(selectedStage?.id, data)}
        title="Chỉnh sửa bước"
        initialData={selectedStage}
        loading={loading}
        existingStages={sortedStages}
      />
    </>
  );
}

// ----------------------------------------------------------------------

function StageCard({ stage, isSelected, onClick, onEdit, onDelete }) {
  const statusInfo = getOrderStatusInfo(stage.status_code);
  
  return (
    <Card
      variant={isSelected ? 'elevation' : 'outlined'}
      sx={{
        p: 2,
        cursor: 'pointer',
        border: isSelected ? 2 : 1,
        borderColor: isSelected ? 'primary.main' : 'divider',
        '&:hover': {
          borderColor: 'primary.main',
          bgcolor: 'action.hover'
        }
      }}
      onClick={onClick}
    >
      <Stack direction="row" alignItems="center" justifyContent="space-between">
        <Box flex={1}>
          <Stack direction="row" alignItems="center" spacing={1} sx={{ mb: 1 }}>
            <Typography variant="subtitle2">
              {stage.name}
            </Typography>
            <Chip
              label={statusInfo?.label || stage.status_code}
              size="small"
              color={statusInfo?.color || 'default'}
              variant="outlined"
            />
          </Stack>
          
          <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
            {stage.is_start_stage && (
              <Label color="info" size="small">Bắt đầu</Label>
            )}
            {stage.is_end_stage && (
              <Label color="success" size="small">Kết thúc</Label>
            )}
            {stage.requires_payment && (
              <Label color="warning" size="small">Cần thanh toán</Label>
            )}
            {stage.requires_inventory && (
              <Label color="secondary" size="small">Cần kho hàng</Label>
            )}
            {stage.auto_transition && (
              <Label color="primary" size="small">Tự động</Label>
            )}
          </Stack>
          
          {stage.description && (
            <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
              {stage.description}
            </Typography>
          )}
        </Box>
        
        <Stack direction="row" spacing={0.5}>
          <Tooltip title="Chỉnh sửa">
            <IconButton size="small" onClick={(e) => { e.stopPropagation(); onEdit(); }}>
              <Iconify icon="solar:pen-bold" />
            </IconButton>
          </Tooltip>
          <Tooltip title="Xóa">
            <IconButton 
              size="small" 
              color="error"
              onClick={(e) => { e.stopPropagation(); onDelete(); }}
            >
              <Iconify icon="solar:trash-bin-trash-bold" />
            </IconButton>
          </Tooltip>
        </Stack>
      </Stack>
    </Card>
  );
}

// ----------------------------------------------------------------------

function StageFormDialog({ 
  open, 
  onClose, 
  onSubmit, 
  title, 
  initialData = null, 
  loading = false,
  existingStages = []
}) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    status_code: '',
    color: 'primary',
    sort_order: existingStages.length + 1,
    is_start_stage: false,
    is_end_stage: false,
    requires_payment: false,
    requires_inventory: false,
    auto_transition: false,
    notify_customer: true,
    notify_admin: false,
    ...initialData
  });

  const handleSubmit = useCallback(async () => {
    if (!formData.name.trim() || !formData.status_code) return;
    
    const result = await onSubmit(formData);
    if (result.success) {
      setFormData({
        name: '',
        description: '',
        status_code: '',
        color: 'primary',
        sort_order: existingStages.length + 1,
        is_start_stage: false,
        is_end_stage: false,
        requires_payment: false,
        requires_inventory: false,
        auto_transition: false,
        notify_customer: true,
        notify_admin: false
      });
    }
  }, [formData, onSubmit, existingStages.length]);

  const handleChange = useCallback((field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  // Lọc các status codes chưa được sử dụng
  const availableStatuses = ORDER_STATUS_OPTIONS.filter(status => 
    !existingStages.some(stage => stage.status_code === status.value) ||
    (initialData && initialData.status_code === status.value)
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
      <DialogTitle>{title}</DialogTitle>
      
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          <Stack direction="row" spacing={2}>
            <TextField
              label="Tên bước"
              value={formData.name}
              onChange={handleChange('name')}
              fullWidth
              required
            />
            
            <TextField
              label="Trạng thái"
              value={formData.status_code}
              onChange={handleChange('status_code')}
              select
              fullWidth
              required
            >
              {availableStatuses.map((status) => (
                <MenuItem key={status.value} value={status.value}>
                  <Stack direction="row" alignItems="center" spacing={1}>
                    <Chip
                      label={status.label}
                      size="small"
                      color={status.color}
                      variant="outlined"
                    />
                  </Stack>
                </MenuItem>
              ))}
            </TextField>
          </Stack>
          
          <TextField
            label="Mô tả"
            value={formData.description}
            onChange={handleChange('description')}
            fullWidth
            multiline
            rows={2}
          />
          
          <Stack direction="row" spacing={2}>
            <TextField
              label="Thứ tự"
              value={formData.sort_order}
              onChange={handleChange('sort_order')}
              type="number"
              inputProps={{ min: 1 }}
            />
            
            <TextField
              label="Màu sắc"
              value={formData.color}
              onChange={handleChange('color')}
              select
            >
              {['primary', 'secondary', 'info', 'success', 'warning', 'error'].map((color) => (
                <MenuItem key={color} value={color}>
                  <Chip label={color} color={color} size="small" />
                </MenuItem>
              ))}
            </TextField>
          </Stack>
          
          <Stack spacing={2}>
            <Typography variant="subtitle2">Cấu hình bước</Typography>
            
            <Stack direction="row" spacing={2} flexWrap="wrap">
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_start_stage}
                    onChange={handleChange('is_start_stage')}
                  />
                }
                label="Bước bắt đầu"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.is_end_stage}
                    onChange={handleChange('is_end_stage')}
                  />
                }
                label="Bước kết thúc"
              />
            </Stack>
            
            <Stack direction="row" spacing={2} flexWrap="wrap">
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.requires_payment}
                    onChange={handleChange('requires_payment')}
                  />
                }
                label="Yêu cầu thanh toán"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.requires_inventory}
                    onChange={handleChange('requires_inventory')}
                  />
                }
                label="Yêu cầu kho hàng"
              />
            </Stack>
            
            <Stack direction="row" spacing={2} flexWrap="wrap">
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.notify_customer}
                    onChange={handleChange('notify_customer')}
                  />
                }
                label="Thông báo khách hàng"
              />
              
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.notify_admin}
                    onChange={handleChange('notify_admin')}
                  />
                }
                label="Thông báo admin"
              />
            </Stack>
          </Stack>
        </Stack>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Hủy</Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={!formData.name.trim() || !formData.status_code || loading}
        >
          {initialData ? 'Cập nhật' : 'Thêm bước'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
