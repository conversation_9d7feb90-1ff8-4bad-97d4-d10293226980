'use client';

import { useState, useCallback } from 'react';

import {
  <PERSON>,
  <PERSON>,
  Stack,
  Button,
  Typo<PERSON>,
  Container,
  Grid,
  Chip,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  MenuItem,
  FormControlLabel,
  Switch
} from '@mui/material';

import { useBoolean } from 'src/hooks/use-boolean';

import { Iconify } from 'src/components/iconify';
import { CustomBreadcrumbs } from 'src/components/custom-breadcrumbs';

import { useCustomWorkflow } from 'src/actions/mooly-chatbot/use-custom-workflow';

import WorkflowCanvas from './workflow-canvas';
import WorkflowTemplateDialog from './workflow-template-dialog';
import { PRODUCT_TYPE_LABELS } from 'src/actions/mooly-chatbot/order-status-business-rules';

// ----------------------------------------------------------------------

export default function WorkflowBuilderView() {
  const [selectedProductType, setSelectedProductType] = useState('simple');
  const [selectedWorkflow, setSelectedWorkflow] = useState(null);
  
  // Dialogs
  const createDialog = useBoolean();
  const templateDialog = useBoolean();
  
  // Workflow hook
  const {
    workflows,
    templates,
    loading,
    createWorkflow,
    createFromTemplate,
    updateWorkflow,
    deleteWorkflow,
    activeWorkflows,
    workflowsByType
  } = useCustomWorkflow({ productType: selectedProductType });

  // Lấy workflows theo product type hiện tại
  const currentWorkflows = workflowsByType[selectedProductType] || [];

  const handleCreateWorkflow = useCallback(async (workflowData) => {
    const result = await createWorkflow({
      ...workflowData,
      product_type: selectedProductType
    });
    
    if (result.success) {
      createDialog.onFalse();
      setSelectedWorkflow(result.data);
    }
    
    return result;
  }, [createWorkflow, selectedProductType, createDialog]);

  const handleCreateFromTemplate = useCallback(async (templateType, customData) => {
    const result = await createFromTemplate(templateType, {
      ...customData,
      product_type: selectedProductType
    });
    
    if (result.success) {
      templateDialog.onFalse();
      setSelectedWorkflow(result.data);
    }
    
    return result;
  }, [createFromTemplate, selectedProductType, templateDialog]);

  const renderHeader = (
    <Container maxWidth="xl">
      <CustomBreadcrumbs
        heading="Quản lý quy trình đơn hàng"
        subHeading="Tạo và tùy chỉnh quy trình xử lý đơn hàng theo nhu cầu kinh doanh"
        links={[
          { name: 'Dashboard', href: '/' },
          { name: 'Đơn hàng', href: '/orders' },
          { name: 'Quy trình' }
        ]}
        action={
          <Stack direction="row" spacing={1}>
            <Button
              variant="outlined"
              startIcon={<Iconify icon="solar:document-add-bold" />}
              onClick={templateDialog.onTrue}
            >
              Từ mẫu có sẵn
            </Button>
            <Button
              variant="contained"
              startIcon={<Iconify icon="solar:add-circle-bold" />}
              onClick={createDialog.onTrue}
            >
              Tạo quy trình mới
            </Button>
          </Stack>
        }
      />
    </Container>
  );

  const renderProductTypeSelector = (
    <Container maxWidth="xl">
      <Card sx={{ p: 3, mb: 3 }}>
        <Typography variant="h6" sx={{ mb: 2 }}>
          Chọn loại sản phẩm
        </Typography>
        <Stack direction="row" spacing={1} flexWrap="wrap" useFlexGap>
          {Object.entries(PRODUCT_TYPE_LABELS).map(([type, label]) => (
            type !== 'all' && (
              <Chip
                key={type}
                label={label}
                variant={selectedProductType === type ? 'filled' : 'outlined'}
                color={selectedProductType === type ? 'primary' : 'default'}
                onClick={() => setSelectedProductType(type)}
                sx={{ mb: 1 }}
              />
            )
          ))}
        </Stack>
      </Card>
    </Container>
  );

  const renderWorkflowList = (
    <Container maxWidth="xl">
      <Grid container spacing={3}>
        <Grid item xs={12} md={4}>
          <Card sx={{ p: 3, height: 'fit-content' }}>
            <Typography variant="h6" sx={{ mb: 2 }}>
              Danh sách quy trình
              <Chip 
                label={currentWorkflows.length} 
                size="small" 
                sx={{ ml: 1 }} 
              />
            </Typography>
            
            <Stack spacing={2}>
              {currentWorkflows.map((workflow) => (
                <Card
                  key={workflow.id}
                  variant={selectedWorkflow?.id === workflow.id ? 'elevation' : 'outlined'}
                  sx={{
                    p: 2,
                    cursor: 'pointer',
                    border: selectedWorkflow?.id === workflow.id ? 2 : 1,
                    borderColor: selectedWorkflow?.id === workflow.id ? 'primary.main' : 'divider',
                    '&:hover': {
                      borderColor: 'primary.main',
                      bgcolor: 'action.hover'
                    }
                  }}
                  onClick={() => setSelectedWorkflow(workflow)}
                >
                  <Stack direction="row" alignItems="center" justifyContent="space-between">
                    <Box>
                      <Typography variant="subtitle2" noWrap>
                        {workflow.name}
                      </Typography>
                      <Typography variant="body2" color="text.secondary" noWrap>
                        {workflow.stages?.length || 0} bước
                      </Typography>
                    </Box>
                    
                    <Stack direction="row" spacing={0.5}>
                      {workflow.is_default && (
                        <Chip label="Mặc định" size="small" color="success" />
                      )}
                      {!workflow.is_active && (
                        <Chip label="Tạm dừng" size="small" color="error" />
                      )}
                    </Stack>
                  </Stack>
                  
                  {workflow.description && (
                    <Typography 
                      variant="caption" 
                      color="text.secondary" 
                      sx={{ mt: 1, display: 'block' }}
                    >
                      {workflow.description}
                    </Typography>
                  )}
                </Card>
              ))}
              
              {currentWorkflows.length === 0 && (
                <Box textAlign="center" py={4}>
                  <Typography variant="body2" color="text.secondary">
                    Chưa có quy trình nào cho {PRODUCT_TYPE_LABELS[selectedProductType]}
                  </Typography>
                  <Button
                    variant="outlined"
                    size="small"
                    startIcon={<Iconify icon="solar:add-circle-bold" />}
                    onClick={templateDialog.onTrue}
                    sx={{ mt: 2 }}
                  >
                    Tạo từ mẫu
                  </Button>
                </Box>
              )}
            </Stack>
          </Card>
        </Grid>
        
        <Grid item xs={12} md={8}>
          <Card sx={{ p: 3, minHeight: 600 }}>
            {selectedWorkflow ? (
              <WorkflowCanvas 
                workflow={selectedWorkflow}
                onUpdate={(updatedWorkflow) => setSelectedWorkflow(updatedWorkflow)}
              />
            ) : (
              <Box
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                height={400}
                color="text.secondary"
              >
                <Iconify icon="solar:document-text-bold" width={64} sx={{ mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Chọn quy trình để chỉnh sửa
                </Typography>
                <Typography variant="body2" textAlign="center">
                  Chọn một quy trình từ danh sách bên trái để xem và chỉnh sửa chi tiết
                </Typography>
              </Box>
            )}
          </Card>
        </Grid>
      </Grid>
    </Container>
  );

  return (
    <>
      <Box sx={{ py: 3 }}>
        {renderHeader}
        {renderProductTypeSelector}
        {renderWorkflowList}
      </Box>

      {/* Create Workflow Dialog */}
      <CreateWorkflowDialog
        open={createDialog.value}
        onClose={createDialog.onFalse}
        onSubmit={handleCreateWorkflow}
        productType={selectedProductType}
        loading={loading}
      />

      {/* Template Dialog */}
      <WorkflowTemplateDialog
        open={templateDialog.value}
        onClose={templateDialog.onFalse}
        onSubmit={handleCreateFromTemplate}
        templates={templates}
        productType={selectedProductType}
        loading={loading}
      />
    </>
  );
}

// ----------------------------------------------------------------------

function CreateWorkflowDialog({ open, onClose, onSubmit, productType, loading }) {
  const [formData, setFormData] = useState({
    name: '',
    description: '',
    is_active: true
  });

  const handleSubmit = useCallback(async () => {
    if (!formData.name.trim()) return;
    
    const result = await onSubmit(formData);
    if (result.success) {
      setFormData({ name: '', description: '', is_active: true });
    }
  }, [formData, onSubmit]);

  const handleChange = useCallback((field) => (event) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
  }, []);

  return (
    <Dialog open={open} onClose={onClose} maxWidth="sm" fullWidth>
      <DialogTitle>
        Tạo quy trình mới cho {PRODUCT_TYPE_LABELS[productType]}
      </DialogTitle>
      
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          <TextField
            label="Tên quy trình"
            value={formData.name}
            onChange={handleChange('name')}
            fullWidth
            required
            placeholder="VD: Quy trình bán hàng chuẩn"
          />
          
          <TextField
            label="Mô tả"
            value={formData.description}
            onChange={handleChange('description')}
            fullWidth
            multiline
            rows={3}
            placeholder="Mô tả chi tiết về quy trình này..."
          />
          
          <FormControlLabel
            control={
              <Switch
                checked={formData.is_active}
                onChange={handleChange('is_active')}
              />
            }
            label="Kích hoạt ngay"
          />
        </Stack>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Hủy</Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={!formData.name.trim() || loading}
        >
          Tạo quy trình
        </Button>
      </DialogActions>
    </Dialog>
  );
}
