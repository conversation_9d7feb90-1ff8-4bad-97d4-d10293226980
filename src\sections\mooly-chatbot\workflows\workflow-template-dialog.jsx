'use client';

import { useState, useCallback } from 'react';

import {
  <PERSON>,
  <PERSON>,
  <PERSON>ack,
  Button,
  Typography,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  Grid,
  Chip,
  RadioGroup,
  FormControlLabel,
  Radio,
  Divider
} from '@mui/material';

import { Iconify } from 'src/components/iconify';
import { Label } from 'src/components/label';
import { PRODUCT_TYPE_LABELS } from 'src/actions/mooly-chatbot/order-status-business-rules';


// ----------------------------------------------------------------------

export default function WorkflowTemplateDialog({ 
  open, 
  onClose, 
  onSubmit, 
  templates, 
  productType, 
  loading = false 
}) {
  const [selectedTemplate, setSelectedTemplate] = useState('');
  const [customData, setCustomData] = useState({
    name: '',
    description: ''
  });

  // Lọc templates theo product type
  const availableTemplates = Object.entries(templates).filter(([key, template]) => 
    template.product_type === productType || template.product_type === 'all'
  );

  const handleSubmit = useCallback(async () => {
    if (!selectedTemplate) return;
    
    const result = await onSubmit(selectedTemplate, customData);
    if (result.success) {
      setSelectedTemplate('');
      setCustomData({ name: '', description: '' });
    }
  }, [selectedTemplate, customData, onSubmit]);

  const handleTemplateSelect = useCallback((templateKey) => {
    setSelectedTemplate(templateKey);
    const template = templates[templateKey];
    if (template && !customData.name) {
      setCustomData(prev => ({
        ...prev,
        name: template.name,
        description: template.description
      }));
    }
  }, [templates, customData.name]);

  const handleCustomDataChange = useCallback((field) => (event) => {
    setCustomData(prev => ({ ...prev, [field]: event.target.value }));
  }, []);

  const renderTemplateCard = (templateKey, template) => (
    <Card
      key={templateKey}
      variant={selectedTemplate === templateKey ? 'elevation' : 'outlined'}
      sx={{
        p: 2,
        cursor: 'pointer',
        border: selectedTemplate === templateKey ? 2 : 1,
        borderColor: selectedTemplate === templateKey ? 'primary.main' : 'divider',
        '&:hover': {
          borderColor: 'primary.main',
          bgcolor: 'action.hover'
        }
      }}
      onClick={() => handleTemplateSelect(templateKey)}
    >
      <Stack spacing={2}>
        <Stack direction="row" alignItems="center" justifyContent="space-between">
          <Typography variant="h6">
            {template.name}
          </Typography>
          <Radio
            checked={selectedTemplate === templateKey}
            onChange={() => handleTemplateSelect(templateKey)}
            size="small"
          />
        </Stack>
        
        <Typography variant="body2" color="text.secondary">
          {template.description}
        </Typography>
        
        <Stack direction="row" spacing={1} alignItems="center">
          <Label color="info" size="small">
            {template.stages?.length || 0} bước
          </Label>
          <Label color="primary" size="small">
            {PRODUCT_TYPE_LABELS[template.product_type]}
          </Label>
        </Stack>
        
        <Divider />
        
        <Box>
          <Typography variant="caption" color="text.secondary" sx={{ mb: 1, display: 'block' }}>
            Các bước trong quy trình:
          </Typography>
          <Stack direction="row" spacing={0.5} flexWrap="wrap" useFlexGap>
            {template.stages?.map((stage, index) => (
              <Chip
                key={index}
                label={stage.name}
                size="small"
                color={stage.color || 'default'}
                variant="outlined"
              />
            ))}
          </Stack>
        </Box>
      </Stack>
    </Card>
  );

  return (
    <Dialog open={open} onClose={onClose} maxWidth="lg" fullWidth>
      <DialogTitle>
        <Stack direction="row" alignItems="center" spacing={1}>
          <Iconify icon="solar:document-text-bold" />
          <Typography variant="h6">
            Chọn mẫu quy trình cho {PRODUCT_TYPE_LABELS[productType]}
          </Typography>
        </Stack>
      </DialogTitle>
      
      <DialogContent>
        <Stack spacing={3} sx={{ mt: 1 }}>
          {/* Template Selection */}
          <Box>
            <Typography variant="subtitle2" sx={{ mb: 2 }}>
              Chọn mẫu quy trình
            </Typography>
            
            <Grid container spacing={2}>
              {availableTemplates.map(([templateKey, template]) => (
                <Grid item xs={12} md={6} key={templateKey}>
                  {renderTemplateCard(templateKey, template)}
                </Grid>
              ))}
            </Grid>
            
            {availableTemplates.length === 0 && (
              <Box
                display="flex"
                flexDirection="column"
                alignItems="center"
                justifyContent="center"
                py={4}
                color="text.secondary"
              >
                <Iconify icon="solar:document-add-bold" width={48} sx={{ mb: 2 }} />
                <Typography variant="h6" gutterBottom>
                  Không có mẫu nào
                </Typography>
                <Typography variant="body2" textAlign="center">
                  Hiện tại chưa có mẫu quy trình nào cho {PRODUCT_TYPE_LABELS[productType]}
                </Typography>
              </Box>
            )}
          </Box>
          
          {/* Custom Data */}
          {selectedTemplate && (
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                Tùy chỉnh thông tin
              </Typography>
              
              <Stack spacing={2}>
                <TextField
                  label="Tên quy trình"
                  value={customData.name}
                  onChange={handleCustomDataChange('name')}
                  fullWidth
                  placeholder="Nhập tên tùy chỉnh cho quy trình"
                  helperText="Để trống để sử dụng tên mặc định từ mẫu"
                />
                
                <TextField
                  label="Mô tả"
                  value={customData.description}
                  onChange={handleCustomDataChange('description')}
                  fullWidth
                  multiline
                  rows={3}
                  placeholder="Nhập mô tả tùy chỉnh cho quy trình"
                  helperText="Để trống để sử dụng mô tả mặc định từ mẫu"
                />
              </Stack>
            </Box>
          )}
          
          {/* Preview */}
          {selectedTemplate && templates[selectedTemplate] && (
            <Box>
              <Typography variant="subtitle2" sx={{ mb: 2 }}>
                Xem trước quy trình
              </Typography>
              
              <Card variant="outlined" sx={{ p: 2 }}>
                <Stack spacing={2}>
                  <Typography variant="body2" color="text.secondary">
                    Quy trình sẽ được tạo với {templates[selectedTemplate].stages?.length || 0} bước:
                  </Typography>
                  
                  <Stack spacing={1}>
                    {templates[selectedTemplate].stages?.map((stage, index) => (
                      <Stack key={index} direction="row" alignItems="center" spacing={2}>
                        <Typography variant="body2" color="text.secondary" sx={{ minWidth: 24 }}>
                          {index + 1}.
                        </Typography>
                        <Chip
                          label={stage.name}
                          size="small"
                          color={stage.color || 'default'}
                          variant="outlined"
                        />
                        {stage.is_start_stage && (
                          <Label color="info" size="small">Bắt đầu</Label>
                        )}
                        {stage.is_end_stage && (
                          <Label color="success" size="small">Kết thúc</Label>
                        )}
                        {stage.requires_payment && (
                          <Label color="warning" size="small">Thanh toán</Label>
                        )}
                      </Stack>
                    ))}
                  </Stack>
                </Stack>
              </Card>
            </Box>
          )}
        </Stack>
      </DialogContent>
      
      <DialogActions>
        <Button onClick={onClose}>Hủy</Button>
        <Button
          variant="contained"
          onClick={handleSubmit}
          disabled={!selectedTemplate || loading}
          startIcon={loading ? <Iconify icon="solar:loading-bold" /> : <Iconify icon="solar:add-circle-bold" />}
        >
          {loading ? 'Đang tạo...' : 'Tạo từ mẫu'}
        </Button>
      </DialogActions>
    </Dialog>
  );
}
